<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>



		  CONDITIONAL  MEANS  AND  RMS  CALCULATIONS  FOR
		   PILOTED, BLUFF-BODY and SWIRL FLAMES DATABASE

	         	   By Joshua Kent & Assaad Masri 
			University of Sydney,  October 2004



<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

1. TABLE OF CONTENTS:

	1. Table of Contents
	2. Introduction
	3. List and Explanation of Files
	4. Data Processing
		4.1 Outline
		4.2 Bin Sizing 
		4.3 Reynolds Equations
		4.4 Favre Equations
		4.5 Density Equations
	5. Explanation of Results Files
		5.1 TXT Files
		5.2 MAT file
		5.3 JPEG files
		5.4 IMPORTANT- Known Processing Errors

<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

2. INTRODUCTION


  Conditional Mean calculations for Swirl, Bluff-Body and Piloted Flames database. By Joshua Kent
and Assaad Masri, October 2004, University of Sydney.

  This readme file contains the  details of the processing methods and equations used to 
calculate condition mean data from the instantaneous measurement data files available from the
Thermofluids web page. A short guide to the associated results files is also given. 

It is STRONGLY recommended that this readme file is thoroughly examined before any attempt to
implement or re-process data is made. Any further questions please direct your enquiries to:
	Prof. Assaad Masri,	<EMAIL>

			

DISCLAIMER: No responsibility is assumed by the suppliers of this data for any injury and/or
property damage as a matter of products liability, negligence or otherwise, or from any use or
operation of any methods, products, instructions, or ideas based on these data.


<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

3. LIST OF FILES

 Each ZIP file contains all processed results files. These include the following files,
specific to each axial location:

		* 2 TXT files- These contain the processed (conditional) Mean and RMS text output
		  results. 

		* 3 JPEG images- These images contain raw data scatter plots of each species,
		  temperature and radial location with the processed Reynolds and Favre Means
		  superimposed over the top.

		* 1 MAT file- This contains all raw & processed data that was loaded or 
		  calculated by the solving program in a MatLab Binary Data file.

		* 1 'Radial Means' folder. This will contain TXT files of Radial means. 

See section 5 for a further explaination of these files.




<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

4. DATA PROCESSING 


4.1 Outline

  In all the data files, species mass fractions, mixture fraction and temperature are all listed 
in reference to a cartesian co-ordinate system- both axial and radial location, displaced from a
known datum, are required to complete the image of the flame. 

  By calcualting conditional means, the data is transfered from cartesion system  to  mixture-
fraction space, whereby only axial location is now required. The data is is divided,  with 
reference to mixture fraction, into small  sections, refered to hereon as 'bins'. All data
points that lie within a bin are used to calcuate a mean (and fluctuation) of any species- one
that is valid only within that particular small range of mixuture fraction space (i.e. 
'CONDITIONAL' to a certain range of mixture fraction).

  



4.2 Bin Sizing
	
  As the equations are not open to any alteration, the main element in the data processing is
detemining the width of each bin. The primary consideration when sizing the bins is to ensure
there are enough data points lying within the bin as to allow reasonable caculation of mean
and RMS, whilst maintaining a small enough bin width so as to ensure reasonable conditionality.

Thus, bin sizes are determined by strict adherence to the following three logical operations:

	1. Minimum Bin Width. Each bin is at a minimum of delta_Zi = 0.002.

	2. Minimum No. of Measurements per Bin. Should the number of measurements in the bin
	   be less than 100 (for Piloted and Swirl, 60 for some Bluff files- see section 5),
	   the bin size is expanded by increments of delta_Zi until this minimum no. is reached. 

	3. The maximum allowable bin size on the lean side of the stocihiometric mixture fraction
	   (Zi_c) is delta_Zi = 0.1 x Zi_c. This is enforced even if the minimum quota of 100
	   data points (60 for some bluff) has not been reached. Throughout processing, a record
	   or 'FLAG' is noted each time this logic is enforced. It is possible, due to the
	   reduced number of measurements used, that calculations made within flaged bins will
	   have reduced accuracy.


  Once the bin sizing has been determined the Conditional Means and RMS of the fluctuations are
calcualted for each species using the following equations. Note for all these equations only 
those measurements that lie within the Bin are used- i.e. 'N' does not represent the total no.
of measurements made, but rather the no. of measurements that lie within the specified bin. 





4.3 Calculating REYNOLDS Mean and RMS:


 The Reynolds mean is given as

		Y_bar = [ sum(Y_i) ] / N


 Allowing each data point to be written as

		Y_i = Y_bar + Y_i'


 Where Y_i' is the fluctuation of Y_i from the mean Y_bar. Rearranging this gives:
	
		Y_i' = Y_i - Y_bar


 Which allows calculation of the square of the fluctuation:

		(Y_i')^2 = (Y_i - Y_bar)^2


 The mean of the square can thus be calculated as:

		mean[(Y')^2] = [ sum (Y_i - Y_bar)^2 ] / N


 and taking the square-root of this gives the RMS of the fluctuation:

		Y'rms = { [ sum (Y_i - Y_bar)^2 ] / N }^0.5





4.4 Calculating FAVRE Mean and RMS:


 The Favre mean is given as

		Y_tilde = [ sum(Y_i x p_i) ] / [ N x p_bar ]


  Where p_i is the density of the  individual measurement and p_bar is the average density. 
As with Reynolds averaging, each data point can be written as:

		Y_i = Y_tilde + Y_i"


 Where Y_" is the Favre fluctuation of Y_i from the mean Y_tilde. Rearranging this gives:
	
		Y_i" = Y_i - Y_tilde


 Which allows calculation of the square of the fluctuation:

		(Y_i")^2 = (Y_i - Y_tilde)^2


 The Favre mean of the square can thus be calculated as:

		mean[(Y")^2] = [ sum {p_i x (Y_i - Y_tilde)^2} ] / [ N x p_bar ]


 and taking the square-root of this gives the Favre RMS of the fluctuation:

		Y"rms = { [ sum {p_i x (Y_i - Y_tilde)^2} ] / [ N x p_bar ] }^0.5





4.5 Calculating the DENSITY 

 Density is used for calculating Favre mean and RMS. It is calcualted using standard ideal gas
laws.

 the molecular wieght of a mixture is gvien by:

		MW_mix = 1 / ( sum( Y_i / MW_i ) )


  Where MW_i is the individual species Molecular wieght, and Y_i is the corresponding species Mass
fraction (such that sum(Y_i) = 1). The following species molecular wights have been used for all
calculations presented:

	SPECIES		MW_i (kg/kmol)

	CH4		16.043
	CH3OH		32.042
	O2		31.999
	N2		28.013
	H2		2.016
	CO2		44.011
	CO		28.010
	OH		17.007
	NO		30.006
	H2O		18.016		


 The mixture molecular wieght can then be used to calculate the gas constant:

		R  = 8.31434 / MW_mix

 and hence the density of the mixture is given by:

		p_mix = 101.325 / ( R x T(K) )


 Once implemented in Favre averaging p_mix == p_i (== the density of each instantaneous
measurement), and p_bar = sum(p_i) / N. 



 The bin sizing logic and the above equations can thus be directly implemented into Matlab
funtions to calculate the Conditional Means and RMS fluctiuation

<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>



6. EXPLANATION OF RESULTS FILES


  The ZIP file contains the conditional means for each particluar experiment and flame type.
When extractin the zipped files, expect to find the following for each AXIAL location:
		4x TXT  files
		1x MAT  file
		3x JPEG files
		1x 'Radial Means' folder


6.1 TXT Files

  The four text files associated with each axial location are:

	2x Conditional Means Files: 'axialLOC_CM_Rey.txt' & 'axialLOC_CM_Fav.txt'

	2x Radial Means Files: 'axialLOC_Rey.txt' & 'axialLOC_Fav.txt'

  The conditional means are calculated using the methods outlined in chapters 4 of this 
Readme file. These files list all the processed data along with the number of data flies and 
instantaneous measurements used for the calculation. All species fractions are in percent. The
width of each 'Bin' is listed along with the number of measurements that comprise each Bin. The
RMS is listed BELOW the corresponding mean, ie Mean and RMS is listed consecutively down each 
column.

  The Radial means files list the Reynolds and Favre mean with respect to radius. This 
information is NOT calculated- it has been extracted from the headers of each instantanoeus
measurement data file (also availableon the Thermofluids Web Page). For PILOTED and BLUFF-BODY
flames there are 2 files (REynolds and Favre)  correspinding to each radial location. For SWIRL
these have all be combined into one large file. These files are contained within a subfolder 
called 'Radial Means'.


6.2 MAT File

  The MAT file is a Matlab Binary data file. It contains all information that was loaded by the
calcualtion programs throughout processing (Raw data, file names etc), along with all the solved
conditional means. This is the exact data that was used to generated the conditional means plots
and Text files. It can only be opened using matlab, but does allow the data and results to be
accessed within a Matlab workspace without having to load any instantaneous meaurment files or
run any solving functions.
 

6.3 JPEG Flies 

  There are three JPEG images for each axial location, each containing multiple plots. The plots
each have pertaining title, axes labels and legend. The JPEG are generated with Matlab- a program
not generally associated with this file type, hence ther is some unclarity to the picture. However,
as the plots are generated using the data contained in the conditional means text files it would
be a simple matter to re-generate the plots using excel or some other program should one wish to.



6.4 IMPORTANT NOTES- KNOWN PROCESSING ERRORS

  A processing error is apperent with files associated with:
		b1f1s02	
		b1f2s02
		b2f2s02

 The instantaneous data files associated with these axial locations list them as being comprised 
of  multiple radial locations. This has caused an error in the Matlab solving function, which is
unable to interpret more than one number for radial location. As a result, the program has
returned the Radial location for these cases as being 1mm. The correct locations are:
		b1f1s02.01	R = 12 & 13 mm	
		b1f1s02.02	R =  9 & 10 mm
		b1f2s02.01	R =  8 & 11 mm		
		b2f2s02.01	R =  8 & 10 mm	
		b2f2s02.01	R =  6 & 12 mm

 Note this error only affects the reported Radial MEAN and RMS for these three axial locations. 
All other temperature and species fraction Data remains correct. (This error does not affect 
calculation, as all calculation is based on Mixture fraction (ZI)- see section 4 for further
details)



 IF DURING THE USEAGE OF THIS DATA ANY OTHER PROCESSING ERRORS ARE DETECTED, COULD YOU PLEASE
FORWARD A SUMMARY TO:
		Joshua Kent	<EMAIL>
	or	Assaad Masri	<EMAIL> 	




DISCLAIMER: No responsibility is assumed by the suppliers of this data for any injury and/or
property damage as a matter of products liability, negligence or otherwise, or from any use or
operation of any methods, products, instructions, or ideas based on these data.
	
<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>



