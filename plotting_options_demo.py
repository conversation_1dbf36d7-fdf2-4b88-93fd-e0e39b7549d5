#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示不同绘图选项的使用方法
"""

from comparison_plots import DataComparison, create_comparison_from_files

def demo_separate_plots():
    """演示分离绘图（默认选项）"""
    print("=" * 60)
    print("演示1: 分离绘图（默认选项）")
    print("=" * 60)
    
    # 使用便捷函数，分离绘图
    output_files = create_comparison_from_files(
        computational_file='0.013.csv',
        experimental_file='b4f3 CM/b4f3/Radial Means/b4f3a013_Rey.txt',
        axial_position=13,
        output_filename='demo_separate',
        show_plot=False,
        separate_plots=True  # 分离绘图
    )
    
    print(f"生成的文件: {output_files}")

def demo_combined_plots():
    """演示组合绘图"""
    print("\n" + "=" * 60)
    print("演示2: 组合绘图")
    print("=" * 60)
    
    # 使用便捷函数，组合绘图
    output_files = create_comparison_from_files(
        computational_file='0.013.csv',
        experimental_file='b4f3 CM/b4f3/Radial Means/b4f3a013_Rey.txt',
        axial_position=13,
        output_filename='demo_combined',
        show_plot=False,
        separate_plots=False  # 组合绘图
    )
    
    print(f"生成的文件: {output_files}")

def demo_class_usage():
    """演示使用类的详细控制"""
    print("\n" + "=" * 60)
    print("演示3: 使用类进行详细控制")
    print("=" * 60)
    
    # 创建对比分析对象
    comparison = DataComparison(
        computational_file='0.013.csv',
        experimental_file='b4f3 CM/b4f3/Radial Means/b4f3a013_Rey.txt',
        axial_position=13
    )
    
    # 分步加载数据
    print("加载计算数据...")
    comp_data = comparison.load_computational_data()
    
    print("加载实验数据...")
    exp_data = comparison.load_experimental_data()
    
    if comp_data and exp_data:
        # 查看共同范围
        common_range = comparison.find_common_range()
        print(f"共同范围: {common_range}")
        
        # 创建分离图
        print("\n创建分离图...")
        separate_files = comparison.create_comparison_plots(
            output_filename='demo_class_separate',
            show_plot=False,
            separate_plots=True
        )
        
        # 创建组合图
        print("\n创建组合图...")
        combined_files = comparison.create_comparison_plots(
            output_filename='demo_class_combined',
            show_plot=False,
            separate_plots=False
        )
        
        print(f"分离图文件: {separate_files}")
        print(f"组合图文件: {combined_files}")

def demo_custom_range():
    """演示自定义范围过滤"""
    print("\n" + "=" * 60)
    print("演示4: 自定义范围过滤")
    print("=" * 60)
    
    comparison = DataComparison(
        computational_file='0.013.csv',
        experimental_file='b4f3 CM/b4f3/Radial Means/b4f3a013_Rey.txt',
        axial_position=13
    )
    
    # 加载数据
    comp_data = comparison.load_computational_data()
    exp_data = comparison.load_experimental_data()
    
    if comp_data and exp_data:
        # 定义多个自定义范围
        custom_ranges = [
            (0, 10),    # 0-10mm
            (5, 20),    # 5-20mm
            (10, 25),   # 10-25mm
        ]
        
        for r_min, r_max in custom_ranges:
            print(f"\n处理范围 {r_min}-{r_max}mm:")
            
            # 过滤数据
            comp_filtered = comparison.filter_data_by_range(comp_data, r_min, r_max)
            exp_filtered = comparison.filter_data_by_range(exp_data, r_min, r_max)
            
            print(f"  计算数据点数: {len(comp_filtered['radial'])}")
            print(f"  实验数据点数: {len(exp_filtered['radial'])}")
            
            if len(comp_filtered['radial']) > 0 and len(exp_filtered['radial']) > 0:
                # 可以在这里创建特定范围的图表
                # 这里只是演示数据过滤功能
                pass

def demo_batch_processing():
    """演示批量处理不同选项"""
    print("\n" + "=" * 60)
    print("演示5: 批量处理不同选项")
    print("=" * 60)
    
    # 定义不同的处理选项
    processing_options = [
        {
            'name': '分离图_显示统计',
            'separate_plots': True,
            'output_prefix': 'batch_separate'
        },
        {
            'name': '组合图_简洁输出',
            'separate_plots': False,
            'output_prefix': 'batch_combined'
        }
    ]
    
    for option in processing_options:
        print(f"\n处理选项: {option['name']}")
        
        output_files = create_comparison_from_files(
            computational_file='0.013.csv',
            experimental_file='b4f3 CM/b4f3/Radial Means/b4f3a013_Rey.txt',
            axial_position=13,
            output_filename=option['output_prefix'],
            show_plot=False,
            separate_plots=option['separate_plots']
        )
        
        print(f"  生成文件: {output_files}")

if __name__ == "__main__":
    print("数据对比绘图选项演示")
    print("=" * 60)
    
    # 运行所有演示
    demo_separate_plots()
    demo_combined_plots()
    demo_class_usage()
    demo_custom_range()
    demo_batch_processing()
    
    print("\n" + "=" * 60)
    print("演示完成！请查看生成的图片文件。")
    print("=" * 60)
