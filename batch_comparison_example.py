#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理多个轴向位置的数据对比示例
演示如何使用改进后的comparison_plots模块处理多个数据文件
"""

from comparison_plots import DataComparison, create_comparison_from_files
import os
from pathlib import Path

def batch_process_axial_positions():
    """
    批量处理多个轴向位置的数据对比
    """
    # 定义数据文件映射
    # 格式：{轴向位置: (计算文件, 实验文件)}
    data_files = {
        13: ('0.013.csv', 'b4f3 CM/b4f3/Radial Means/b4f3a013_Rey.txt'),
        # 可以添加更多轴向位置的数据
        # 25: ('0.025.csv', 'b4f3 CM/b4f3/Radial Means/b4f3a025_Rey.txt'),
        # 50: ('0.050.csv', 'b4f3 CM/b4f3/Radial Means/b4f3a050_Rey.txt'),
    }
    
    results = []
    
    for axial_pos, (comp_file, exp_file) in data_files.items():
        print(f"\n{'='*60}")
        print(f"处理轴向位置 {axial_pos}mm 的数据")
        print(f"{'='*60}")
        
        # 检查文件是否存在
        if not os.path.exists(comp_file):
            print(f"警告：计算文件 {comp_file} 不存在，跳过")
            continue
        if not os.path.exists(exp_file):
            print(f"警告：实验文件 {exp_file} 不存在，跳过")
            continue
        
        try:
            # 使用便捷函数创建对比图
            output_file = create_comparison_from_files(
                computational_file=comp_file,
                experimental_file=exp_file,
                axial_position=axial_pos,
                show_plot=False  # 批量处理时不显示图表
            )
            
            results.append({
                'axial_position': axial_pos,
                'output_file': output_file,
                'status': 'success'
            })
            
        except Exception as e:
            print(f"处理轴向位置 {axial_pos}mm 时出错: {e}")
            results.append({
                'axial_position': axial_pos,
                'output_file': None,
                'status': 'error',
                'error': str(e)
            })
    
    # 打印处理结果摘要
    print(f"\n{'='*60}")
    print("批量处理结果摘要")
    print(f"{'='*60}")
    
    for result in results:
        if result['status'] == 'success':
            print(f"✓ 轴向位置 {result['axial_position']}mm: {result['output_file']}")
        else:
            print(f"✗ 轴向位置 {result['axial_position']}mm: 处理失败 - {result.get('error', '未知错误')}")
    
    return results

def single_comparison_example():
    """
    单个对比的详细示例
    """
    print("单个数据对比示例")
    print("-" * 40)
    
    # 方法1：使用类的方式（推荐用于需要更多控制的情况）
    comparison = DataComparison(
        computational_file='0.013.csv',
        experimental_file='b4f3 CM/b4f3/Radial Means/b4f3a013_Rey.txt',
        axial_position=13
    )
    
    # 可以分步骤执行
    comp_data = comparison.load_computational_data()
    exp_data = comparison.load_experimental_data()
    
    if comp_data and exp_data:
        # 创建对比图
        output_file = comparison.create_comparison_plots(
            output_filename='detailed_comparison_13mm.png',
            show_plot=False
        )
        print(f"详细对比图已保存为: {output_file}")

def custom_range_example():
    """
    自定义范围对比示例
    """
    print("\n自定义范围对比示例")
    print("-" * 40)
    
    comparison = DataComparison(
        computational_file='0.013.csv',
        experimental_file='b4f3 CM/b4f3/Radial Means/b4f3a013_Rey.txt',
        axial_position=13
    )
    
    # 加载数据
    comp_data = comparison.load_computational_data()
    exp_data = comparison.load_experimental_data()
    
    if comp_data and exp_data:
        # 手动设置对比范围（例如只看0-15mm范围）
        custom_range = (0, 15)
        comp_filtered = comparison.filter_data_by_range(comp_data, *custom_range)
        exp_filtered = comparison.filter_data_by_range(exp_data, *custom_range)
        
        print(f"自定义范围 {custom_range[0]}-{custom_range[1]}mm:")
        print(f"  计算数据点数: {len(comp_filtered['radial'])}")
        print(f"  实验数据点数: {len(exp_filtered['radial'])}")

if __name__ == "__main__":
    # 运行示例
    print("数据对比工具使用示例")
    print("=" * 60)
    
    # 1. 单个对比示例
    single_comparison_example()
    
    # 2. 自定义范围示例
    custom_range_example()
    
    # 3. 批量处理示例
    batch_results = batch_process_axial_positions()
    
    print(f"\n总共处理了 {len(batch_results)} 个轴向位置")
