#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用的计算结果与实验结果数据对比制图工具
支持温度T和混合物分数Z的对比分析
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
from pathlib import Path

# 设置字体
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

class DataComparison:
    """数据对比分析类"""

    def __init__(self, computational_file, experimental_file, axial_position=None):
        """
        初始化数据对比分析

        Parameters:
        -----------
        computational_file : str
            计算数据文件路径
        experimental_file : str
            实验数据文件路径
        axial_position : float, optional
            轴向位置（mm），用于图表标题
        """
        self.computational_file = computational_file
        self.experimental_file = experimental_file
        self.axial_position = axial_position

        # 数据存储
        self.comp_data = None
        self.exp_data = None
        self.common_range = None

    def load_computational_data(self):
        """
        加载计算数据 (CSV格式)

        Returns:
        --------
        dict : 包含径向坐标、温度和混合物分数的字典
        """
        try:
            # 读取CSV文件
            data = pd.read_csv(self.computational_file)

            radial_coord = data.iloc[:, -1] * 1000  # 转换为mm
            temperature = data['Temperature']  # 温度
            mixture_fraction = data['Mixture-fraction']  # 混合物分数

            self.comp_data = {
                'radial': radial_coord,
                'temperature': temperature,
                'mixture_fraction': mixture_fraction
            }

            print(f"成功加载计算数据: {len(radial_coord)} 个数据点")
            return self.comp_data

        except Exception as e:
            print(f"读取计算数据时出错: {e}")
            return None

    def load_experimental_data(self):
        """
        加载实验数据 (径向平均数据格式)

        Returns:
        --------
        dict : 包含径向坐标、温度和混合物分数的字典
        """
        try:
            # 手动读取文件并解析
            with open(self.experimental_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 找到数据开始的行（第15行是列标题）
            header_line = 14  # 第15行（索引为14）
            data_start = 16  # 第17行开始是数据（索引为16）

            # 解析列标题
            header = lines[header_line].strip().split('\t')
            header = [col.strip() for col in header]
            print(f"实验数据列标题: {header}")

            # 解析数据 - 只取平均值行，跳过RMS行
            data_rows = []
            for i in range(data_start, len(lines)):
                line = lines[i].strip()
                if line and not line.isspace():  # 跳过空行
                    row = line.split('\t')
                    if len(row) >= 3:  # 确保有足够的列
                        # 只取奇数行（平均值），跳过偶数行（RMS值）
                        if (i - data_start) % 2 == 0:  # 偶数索引对应奇数行
                            data_rows.append(row)

            # 转换为DataFrame
            data = pd.DataFrame(data_rows, columns=header[:len(data_rows[0]) if data_rows else 0])

            if data.empty:
                print("没有找到有效的实验数据")
                return None

            # 转换数据类型
            data['R(mm)'] = pd.to_numeric(data['R(mm)'], errors='coerce')
            data['ZI'] = pd.to_numeric(data['ZI'], errors='coerce')
            data['T(K)'] = pd.to_numeric(data['T(K)'], errors='coerce')

            # 提取需要的列
            radial_coord = data['R(mm)']
            mixture_fraction = data['ZI']
            temperature = data['T(K)']

            # 过滤掉空行和无效数据
            valid_mask = ~(pd.isna(mixture_fraction) | pd.isna(temperature) | pd.isna(radial_coord))

            self.exp_data = {
                'radial': radial_coord[valid_mask],
                'temperature': temperature[valid_mask],
                'mixture_fraction': mixture_fraction[valid_mask]
            }

            print(f"成功加载实验数据: {len(self.exp_data['radial'])} 个数据点")
            return self.exp_data

        except Exception as e:
            print(f"读取实验数据时出错: {e}")
            import traceback
            traceback.print_exc()
            return None

    def find_common_range(self):
        """
        找到计算数据和实验数据的共同径向坐标范围

        Returns:
        --------
        tuple : (min_r, max_r) 共同范围的最小值和最大值
        """
        if self.comp_data is None or self.exp_data is None:
            print("请先加载数据")
            return None

        comp_min, comp_max = self.comp_data['radial'].min(), self.comp_data['radial'].max()
        exp_min, exp_max = self.exp_data['radial'].min(), self.exp_data['radial'].max()

        # 找到重叠区间
        common_min = max(comp_min, exp_min)
        common_max = min(comp_max, exp_max)

        if common_min >= common_max:
            print("警告：计算数据和实验数据没有重叠的径向坐标范围")
            return None

        self.common_range = (common_min, common_max)
        print(f"共同径向坐标范围: {common_min:.3f} - {common_max:.3f} mm")
        return self.common_range

    def filter_data_by_range(self, data_dict, r_min, r_max):
        """
        根据径向坐标范围过滤数据

        Parameters:
        -----------
        data_dict : dict
            包含径向坐标、温度和混合物分数的数据字典
        r_min, r_max : float
            径向坐标的最小值和最大值

        Returns:
        --------
        dict : 过滤后的数据字典
        """
        mask = (data_dict['radial'] >= r_min) & (data_dict['radial'] <= r_max)

        filtered_data = {
            'radial': data_dict['radial'][mask],
            'temperature': data_dict['temperature'][mask],
            'mixture_fraction': data_dict['mixture_fraction'][mask]
        }

        return filtered_data

    def create_comparison_plots(self, output_filename=None, show_plot=True, separate_plots=True):
        """
        创建对比图

        Parameters:
        -----------
        output_filename : str, optional
            输出文件名前缀，如果为None则自动生成
        show_plot : bool, optional
            是否显示图表，默认为True
        separate_plots : bool, optional
            是否分开绘制两个图，默认为True
        """
        # 加载数据
        print("正在加载数据...")
        comp_data = self.load_computational_data()
        exp_data = self.load_experimental_data()

        if comp_data is None or exp_data is None:
            print("数据加载失败，请检查文件路径和格式")
            return

        # 找到共同范围
        common_range = self.find_common_range()
        if common_range is None:
            print("无法找到共同的径向坐标范围，将显示所有数据")
            comp_filtered = comp_data
            exp_filtered = exp_data
        else:
            # 过滤数据到共同范围
            r_min, r_max = common_range
            comp_filtered = self.filter_data_by_range(comp_data, r_min, r_max)
            exp_filtered = self.filter_data_by_range(exp_data, r_min, r_max)

            print(f"过滤后的计算数据点数: {len(comp_filtered['radial'])}")
            print(f"过滤后的实验数据点数: {len(exp_filtered['radial'])}")

        # 生成基础文件名
        if output_filename is None:
            if self.axial_position:
                base_filename = f'comparison_{self.axial_position}mm'
            else:
                base_filename = 'comparison'
        else:
            # 移除扩展名作为基础文件名
            base_filename = os.path.splitext(output_filename)[0]

        title_suffix = f" at Axial Position {self.axial_position}mm" if self.axial_position else ""

        output_files = []

        if separate_plots:
            # 分别创建两个图
            output_files.extend(self._create_separate_plots(comp_filtered, exp_filtered,
                                                           base_filename, title_suffix, show_plot))
        else:
            # 创建组合图
            output_file = self._create_combined_plot(comp_filtered, exp_filtered,
                                                   base_filename, title_suffix, show_plot)
            output_files.append(output_file)

        # 打印数据统计信息
        self.print_statistics(comp_filtered, exp_filtered)

        return output_files

    def _create_separate_plots(self, comp_filtered, exp_filtered, base_filename, title_suffix, show_plot):
        """创建分离的温度和混合物分数对比图"""
        output_files = []

        # 图1：温度对比
        plt.figure(figsize=(10, 6))
        plt.scatter(comp_filtered['radial'], comp_filtered['temperature'],
                   alpha=0.7, s=40, label='Computational Results', color='red', marker='o')
        plt.scatter(exp_filtered['radial'], exp_filtered['temperature'],
                   alpha=0.7, s=40, label='Experimental Results', color='blue', marker='s')
        plt.xlabel('Radial Coordinate (mm)')
        plt.ylabel('Temperature T (K)')
        plt.title(f'Temperature Distribution Comparison{title_suffix}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        temp_filename = f'{base_filename}_temperature.png'
        plt.savefig(temp_filename, dpi=300, bbox_inches='tight')
        print(f"温度对比图已保存为: {temp_filename}")
        output_files.append(temp_filename)

        if show_plot:
            plt.show()
        else:
            plt.close()

        # 图2：混合物分数对比
        plt.figure(figsize=(10, 6))
        plt.scatter(comp_filtered['radial'], comp_filtered['mixture_fraction'],
                   alpha=0.7, s=40, label='Computational Results', color='red', marker='o')
        plt.scatter(exp_filtered['radial'], exp_filtered['mixture_fraction'],
                   alpha=0.7, s=40, label='Experimental Results', color='blue', marker='s')
        plt.xlabel('Radial Coordinate (mm)')
        plt.ylabel('Mixture Fraction Z')
        plt.title(f'Mixture Fraction Distribution Comparison{title_suffix}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        mixture_filename = f'{base_filename}_mixture_fraction.png'
        plt.savefig(mixture_filename, dpi=300, bbox_inches='tight')
        print(f"混合物分数对比图已保存为: {mixture_filename}")
        output_files.append(mixture_filename)

        if show_plot:
            plt.show()
        else:
            plt.close()

        return output_files

    def _create_combined_plot(self, comp_filtered, exp_filtered, base_filename, title_suffix, show_plot):
        """创建组合的温度和混合物分数对比图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 图1：温度对比
        ax1.scatter(comp_filtered['radial'], comp_filtered['temperature'],
                   alpha=0.7, s=30, label='Computational Results', color='red', marker='o')
        ax1.scatter(exp_filtered['radial'], exp_filtered['temperature'],
                   alpha=0.7, s=30, label='Experimental Results', color='blue', marker='s')
        ax1.set_xlabel('Radial Coordinate (mm)')
        ax1.set_ylabel('Temperature T (K)')
        ax1.set_title(f'Temperature Distribution Comparison{title_suffix}')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 图2：混合物分数对比
        ax2.scatter(comp_filtered['radial'], comp_filtered['mixture_fraction'],
                   alpha=0.7, s=30, label='Computational Results', color='red', marker='o')
        ax2.scatter(exp_filtered['radial'], exp_filtered['mixture_fraction'],
                   alpha=0.7, s=30, label='Experimental Results', color='blue', marker='s')
        ax2.set_xlabel('Radial Coordinate (mm)')
        ax2.set_ylabel('Mixture Fraction Z')
        ax2.set_title(f'Mixture Fraction Distribution Comparison{title_suffix}')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 调整布局
        plt.tight_layout()

        combined_filename = f'{base_filename}_combined.png'
        plt.savefig(combined_filename, dpi=300, bbox_inches='tight')
        print(f"组合对比图已保存为: {combined_filename}")

        if show_plot:
            plt.show()
        else:
            plt.close()

        return combined_filename

    def print_statistics(self, comp_data, exp_data):
        """
        打印数据统计信息

        Parameters:
        -----------
        comp_data : dict
            计算数据字典
        exp_data : dict
            实验数据字典
        """
        print("\n=== Data Statistics ===")
        print(f"Computational data points: {len(comp_data['radial'])}")
        print(f"Experimental data points: {len(exp_data['radial'])}")

        print(f"\nRadial coordinate range:")
        print(f"  Computational: {comp_data['radial'].min():.3f} - {comp_data['radial'].max():.3f} mm")
        print(f"  Experimental:  {exp_data['radial'].min():.3f} - {exp_data['radial'].max():.3f} mm")

        print(f"\nTemperature range:")
        print(f"  Computational: {comp_data['temperature'].min():.1f} - {comp_data['temperature'].max():.1f} K")
        print(f"  Experimental:  {exp_data['temperature'].min():.1f} - {exp_data['temperature'].max():.1f} K")

        print(f"\nMixture fraction range:")
        print(f"  Computational: {comp_data['mixture_fraction'].min():.4f} - {comp_data['mixture_fraction'].max():.4f}")
        print(f"  Experimental:  {exp_data['mixture_fraction'].min():.4f} - {exp_data['mixture_fraction'].max():.4f}")


def create_comparison_from_files(computational_file, experimental_file, axial_position=None,
                                output_filename=None, show_plot=True, separate_plots=True):
    """
    便捷函数：从文件创建对比图

    Parameters:
    -----------
    computational_file : str
        计算数据文件路径
    experimental_file : str
        实验数据文件路径
    axial_position : float, optional
        轴向位置（mm）
    output_filename : str, optional
        输出文件名前缀
    show_plot : bool, optional
        是否显示图表
    separate_plots : bool, optional
        是否分开绘制两个图，默认为True

    Returns:
    --------
    list : 输出文件名列表
    """
    comparison = DataComparison(computational_file, experimental_file, axial_position)
    return comparison.create_comparison_plots(output_filename, show_plot, separate_plots)


if __name__ == "__main__":
    # 示例用法：轴向位置13mm的对比
    computational_file = '0.013.csv'
    experimental_file = 'b4f3 CM/b4f3/Radial Means/b4f3a013_Rey.txt'

    # 使用类的方式
    comparison = DataComparison(computational_file, experimental_file, axial_position=13)
    comparison.create_comparison_plots()

    # 或者使用便捷函数
    # create_comparison_from_files(computational_file, experimental_file,
    #                             axial_position=13, show_plot=False)
