#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
计算结果与实验结果的数据对比制图
轴向位置13mm处的温度T和混合物分数Z对比
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_computational_data(filename):
    """
    加载计算数据 (0.013.csv)
    """
    try:
        # 读取CSV文件
        data = pd.read_csv(filename)
        
        # 提取需要的列
        # 根据文件内容，最后一列是径向坐标 (Points:2)
        # Temperature 是温度列
        # Mixture-fraction 是混合物分数列
        radial_coord = data.iloc[:, -1]  # 最后一列：径向坐标
        temperature = data['Temperature']  # 温度
        mixture_fraction = data['Mixture-fraction']  # 混合物分数
        
        return radial_coord, temperature, mixture_fraction
    except Exception as e:
        print(f"读取计算数据时出错: {e}")
        return None, None, None

def load_experimental_data(filename):
    """
    加载实验数据 (b4f3a013_CM_Fav.txt)
    """
    try:
        # 手动读取文件并解析
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # 找到数据开始的行（第10行是列标题）
        header_line = 9  # 第10行（索引为9）
        data_start = 11  # 第12行开始是数据（索引为11）

        # 解析列标题
        header = lines[header_line].strip().split('\t')
        header = [col.strip() for col in header]
        print(f"列标题: {header}")

        # 解析数据
        data_rows = []
        for i in range(data_start, len(lines)):
            line = lines[i].strip()
            if line and not line.isspace():  # 跳过空行
                row = line.split('\t')
                if len(row) >= 12:  # 确保有足够的列
                    data_rows.append(row)

        # 转换为DataFrame
        data = pd.DataFrame(data_rows, columns=header[:len(data_rows[0])])

        # 转换数据类型
        data['ZI'] = pd.to_numeric(data['ZI'], errors='coerce')
        data['T(K)'] = pd.to_numeric(data['T(K)'], errors='coerce')
        data['R(mm)'] = pd.to_numeric(data['R(mm)'], errors='coerce')

        # 提取需要的列
        mixture_fraction = data['ZI']
        temperature = data['T(K)']
        radial_coord = data['R(mm)']

        # 过滤掉空行和无效数据
        valid_mask = ~(pd.isna(mixture_fraction) | pd.isna(temperature) | pd.isna(radial_coord))

        return radial_coord[valid_mask], temperature[valid_mask], mixture_fraction[valid_mask]
    except Exception as e:
        print(f"读取实验数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

def create_comparison_plots():
    """
    创建对比图
    """
    # 加载数据
    print("正在加载计算数据...")
    comp_r, comp_T, comp_Z = load_computational_data('0.013.csv')
    
    print("正在加载实验数据...")
    exp_r, exp_T, exp_Z = load_experimental_data('b4f3 CM/b4f3/b4f3a013_CM_Fav.txt')
    
    if comp_r is None or exp_r is None:
        print("数据加载失败，请检查文件路径和格式")
        return
    
    # 转换单位：计算数据的径向坐标可能需要转换为mm
    # 根据数据观察，计算数据的径向坐标单位可能是m，需要转换为mm
    comp_r_mm = comp_r * 1000  # 转换为mm
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 图1：温度对比
    ax1.scatter(comp_r_mm, comp_T, alpha=0.6, s=20, label='计算结果', color='red', marker='o')
    ax1.scatter(exp_r, exp_T, alpha=0.6, s=20, label='实验结果', color='blue', marker='s')
    ax1.set_xlabel('径向坐标 (mm)')
    ax1.set_ylabel('温度 T (K)')
    ax1.set_title('轴向位置13mm处温度分布对比')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 图2：混合物分数对比
    ax2.scatter(comp_r_mm, comp_Z, alpha=0.6, s=20, label='计算结果', color='red', marker='o')
    ax2.scatter(exp_r, exp_Z, alpha=0.6, s=20, label='实验结果', color='blue', marker='s')
    ax2.set_xlabel('径向坐标 (mm)')
    ax2.set_ylabel('混合物分数 Z')
    ax2.set_title('轴向位置13mm处混合物分数分布对比')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('temperature_mixture_comparison_13mm.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印数据统计信息
    print("\n数据统计信息:")
    print(f"计算数据点数: {len(comp_r)}")
    print(f"实验数据点数: {len(exp_r)}")
    print(f"计算数据径向坐标范围: {comp_r_mm.min():.3f} - {comp_r_mm.max():.3f} mm")
    print(f"实验数据径向坐标范围: {exp_r.min():.3f} - {exp_r.max():.3f} mm")
    print(f"计算数据温度范围: {comp_T.min():.1f} - {comp_T.max():.1f} K")
    print(f"实验数据温度范围: {exp_T.min():.1f} - {exp_T.max():.1f} K")
    print(f"计算数据混合物分数范围: {comp_Z.min():.4f} - {comp_Z.max():.4f}")
    print(f"实验数据混合物分数范围: {exp_Z.min():.4f} - {exp_Z.max():.4f}")

if __name__ == "__main__":
    create_comparison_plots()
